import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { PROGRAMMING_ENDPOINTS } from '../endpoints';

export interface IProgramming {
	id: string;
	id_devices: string[];
	id_presentations: string[];
	createdAt: string;
	updatedAt: string;
}

export interface IProgrammingFindAllResponse {
	data: IProgramming[];
	total: number;
	page: number;
	pageSize: number;
	totalPages: number;
}

export interface IFindAllProgrammingParams {
	page?: number;
	pageSize?: number;
	search?: string;
}

export const findAllProgramming = async (params?: IFindAllProgrammingParams): Promise<ApiResponse<IProgrammingFindAllResponse>> => {
	try {
		const searchParams = new URLSearchParams();

		if (params?.page) {
			searchParams.append('page', params.page.toString());
		}

		if (params?.pageSize) {
			searchParams.append('pageSize', params.pageSize.toString());
		}

		if (params?.search) {
			searchParams.append('search', params.search);
		}

		const url = searchParams.toString() ? `${PROGRAMMING_ENDPOINTS.FIND_ALL}?${searchParams.toString()}` : PROGRAMMING_ENDPOINTS.FIND_ALL;

		const response = await api.get<IProgrammingFindAllResponse>(url);
		return { success: true, data: response.data, status: response.status };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
