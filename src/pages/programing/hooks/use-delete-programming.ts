import { deleteProgramming } from '@/pages/programing/api/requests/delete';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export const useDeleteProgramming = (onSuccess?: () => void) => {
	const queryClient = useQueryClient();

	const { mutateAsync, isPending } = useMutation({
		mutationKey: ['delete-programming'],
		mutationFn: async (id: string) => {
			const response = await deleteProgramming(id);
			if (!response.success) throw new Error(response.data?.message || 'Erro ao deletar programação');
			return response.data;
		},
		onSuccess: () => {
			toast.success('Programação deletada com sucesso!');
			onSuccess?.();
			queryClient.invalidateQueries({
				queryKey: ['programming'],
				exact: false,
			});
		},
		onError: (error: Error) => {
			toast.error(error.message);
		},
	});

	return {
		deleteProgramming: mutateAsync,
		isLoading: isPending,
	};
};
