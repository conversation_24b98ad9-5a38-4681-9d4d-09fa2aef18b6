import { createProgramming, ICreateProgrammingDto } from '@/pages/programing/api/requests/create';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

interface CreateProgrammingParams {
	data: ICreateProgrammingDto;
}

export const useCreateProgramming = (onSuccess?: () => void) => {
	const queryClient = useQueryClient();

	const { mutateAsync, isPending } = useMutation({
		mutationKey: ['create-programming'],
		mutationFn: async ({ data }: CreateProgrammingParams) => {
			const response = await createProgramming({ items: data });
			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
		onSuccess: () => {
			toast.success('Programação criada com sucesso!');
			onSuccess?.();
			queryClient.invalidateQueries({
				queryKey: ['programming'],
				exact: false,
			});
		},
		onError: (error: Error) => {
			toast.error(error.message);
		},
	});

	return {
		createProgramming: mutateAsync,
		isLoading: isPending,
	};
};
